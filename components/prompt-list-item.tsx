"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Heart, Bookmark as BookmarkIcon, Eye, Sparkles, MessageSquare } from "lucide-react" // Renamed Bookmark to BookmarkIcon
import { getIconColorHex } from "@/lib/utils/category-styles"
import { useState, useCallback, useEffect } from "react"
import { getCategoryTheme } from "./category-icon-mapper"
import PromptThumbnail from "./prompt-thumbnail"
import { generatePromptUrl } from "@/lib/utils/url-helpers"
import AddToCollectionDialog from "./add-to-collection-dialog" // Import the dialog component
import type { PromptCard as PromptCardType } from "@/lib/types" // Import PromptCardType
import { useUser } from "@/lib/hooks/use-user"
import slugify from 'slugify'

interface PromptListItemProps {
  prompt: PromptCardType; // Use PromptCardType
  isSaved?: boolean; 
  onToggleSave?: (promptId: string, currentSaveStatus: boolean) => Promise<void>; // Added save toggle handler
}

export default function PromptListItem({ prompt, isSaved: propIsSaved, onToggleSave }: PromptListItemProps) { // Added new props
  const [isLiked, setIsLiked] = useState(false)
  const [likeCount, setLikeCount] = useState(prompt.likeCount)
  const [isAddToCollectionDialogOpen, setIsAddToCollectionDialogOpen] = useState(false);
  const { user } = useUser()
  
  // Use prop override if provided, otherwise use prompt's saved status
  // For logged-in users, prioritize the prop value, for non-logged-in users, always false
  const isSaved = user ? (propIsSaved !== undefined ? propIsSaved : prompt.isSaved) : false;

  useEffect(() => {
    // This effect can be used to sync local state if propIsSaved changes, if necessary
    // For now, isSaved directly uses propIsSaved if available.
  }, [propIsSaved]);

  // Get the theme for this category
  const theme = getCategoryTheme(prompt.category?.name || "Other") // Added optional chaining and fallback
  const { icon: Icon } = theme
  const categoryColor = theme.colors

  // Check if the current user is the prompt author
  const isOwnPrompt = user?.id === prompt.user?.id

  const handleLike = () => {
    if (isLiked) {
      setLikeCount((likeCount ?? 0) - 1)
    } else {
      setLikeCount((likeCount ?? 0) + 1)
    }
    setIsLiked(!isLiked)
  }

  // Format large numbers with k/M suffix
  const formatNumber = (num: number | undefined | null) => {
    if (!num) return "0"
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M"
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + "k"
    }
    return num.toString()
  }

  // Format date
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "";
    const date = new Date(dateString)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    }).format(date)
  }

  const promptUrl = prompt.shortId ? generatePromptUrl(prompt) : `/prompt/${prompt.id}`

  return (
    <Card className="w-full overflow-hidden transition-all hover:border-accent-green/30 hover:shadow-sm hover:shadow-accent-green/10 relative">
      {/* Bookmark/Add to Collection positioned at top right of card */}
      <div className="absolute top-4 right-4 z-20">
        <Button
          variant="ghost"
          size="icon"
          className={`h-8 w-8 bg-black/40 backdrop-blur-sm hover:bg-black/60 transition-all duration-200 ${
            isSaved
              ? 'text-accent-green hover:text-accent-green/80'
              : 'text-white hover:text-accent-green'
          }`}
          onClick={(e) => {
            e.preventDefault(); // Prevent link navigation
            e.stopPropagation(); // Stop event bubbling
            if (onToggleSave && isSaved) { // If already saved, behavior might be to unsave or open dialog
              // For now, always open dialog to manage collections
              setIsAddToCollectionDialogOpen(true);
            } else {
              setIsAddToCollectionDialogOpen(true);
            }
          }}
          title={isSaved ? "Manage collections" : "Save to collection"}
          aria-label={isSaved ? "Edit collections for this prompt" : "Save this prompt to a collection"}
        >
          <BookmarkIcon className={`h-4 w-4 ${isSaved ? 'fill-current' : ''}`} />
        </Button>
      </div>
      
      {/* Corner ribbon with remix count - only show if remixCount > 0 */}
      {prompt.remixCount !== undefined && prompt.remixCount > 0 && (
        <div className="absolute top-0 right-0 z-10">
          <div className="bg-purple-600 text-white text-xs px-2 py-1 flex items-center gap-1 rounded-bl-md">
            <Sparkles className="h-3 w-3" />
            <span>{formatNumber(prompt.remixCount)}</span>
          </div>
        </div>
      )}

      <CardContent className="flex gap-4 p-4">
        {/* Thumbnail */}
        <div className="hidden sm:block relative">
          <Link href={promptUrl}>
            <PromptThumbnail
              title={prompt.title}
              category={prompt.category?.name || "Other"}
              imageUrl={prompt.imageUrl}
              className="h-24 w-24"
              placeholderType="icon"
              iconSizeClass="h-6 w-6"
              labelSizeClass="text-xs"
            />
          </Link>
        </div>

        {/* Content */}
        <div className="flex flex-1 flex-col">
          <div className="mb-1 flex items-center gap-2">
            <Link href={`/user/${prompt.user?.username}`} className="flex items-center gap-1"> {/* Added optional chaining */}
              <Avatar className="h-5 w-5">
                <AvatarImage src={prompt.user?.avatarUrl || "/placeholder-user.jpg"} alt={prompt.user?.username || "User"} /> {/* Added optional chaining and fallback */}
                <AvatarFallback>{prompt.user?.username?.charAt(0).toUpperCase() || "U"}</AvatarFallback> {/* Added optional chaining and fallback */}
              </Avatar>
              <span className="text-xs font-medium">{prompt.user?.username || "Anonymous"}</span> {/* Added optional chaining and fallback */}
            </Link>
            <span className="text-xs text-muted-foreground">•</span>
            <span className="text-xs text-muted-foreground">{formatDate(prompt.createdAt)}</span>

            {prompt.isPremium && (
              <>
                <span className="text-xs text-muted-foreground">•</span>
                <Badge variant="outline" className="bg-yellow-500/10 text-yellow-500 text-xs">
                  Premium
                </Badge>
              </>
            )}
          </div>

          {/* Category and Tool badges */}
          <div className="flex flex-wrap items-center gap-2 mb-2">
            <Link href={`/category/${prompt.category?.slug}`}> {/* Added optional chaining */}
              <Badge variant="outline" className={`text-xs`} style={{ color: getIconColorHex(prompt.category?.slug || "other"), borderColor: getIconColorHex(prompt.category?.slug || "other") }}> {/* Added optional chaining and fallback */}
                {prompt.category?.name || "Other"} {/* Added optional chaining and fallback */}
              </Badge>
            </Link>

            {prompt.tool && (
              <Link href={`/tool/${prompt.tool.slug}`}>
                <Badge variant="outline" className="text-xs">
                  {prompt.tool.name}
                </Badge>
              </Link>
            )}
          </div>

          {/* Tags in header - Option C */}
          {prompt.tags && prompt.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-2">
              {prompt.tags.slice(0, 1).map((tag) => {
                // Use tag.slug if it exists, otherwise create a slug from tag.name
                const tagSlug = tag.slug || slugify(tag.name, { lower: true, strict: true })
                return (
                  <Link
                    key={tag.slug || `tag-${tag.name}`}
                    href={`/tag/${tagSlug}`}
                  >
                    <Badge className="text-xs bg-gray-800 hover:bg-gray-700 text-white rounded-full px-2 py-0.5">
                      #{tag.name}
                    </Badge>
                  </Link>
                )
              })}
            </div>
          )}

          <Link href={promptUrl} className="group">
            <h3 className="mb-1 font-semibold group-hover:text-accent-green">{prompt.title}</h3>
            <p className="line-clamp-2 text-sm text-muted-foreground">{prompt.description}</p>
          </Link>

          <div className="mt-2 flex items-center justify-between">
            <div className="flex gap-2">
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Heart className="h-4 w-4 text-pink-500" />
                <span>{formatNumber(prompt.likeCount || prompt.rating || 0)}</span>
              </div>

              {/* Assuming commentCount is available on prompt object */}
              {prompt.commentCount !== undefined && (
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <MessageSquare className="h-4 w-4" />
                  <span>{formatNumber(prompt.commentCount)}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
      {/* Add to Collection Dialog */}
      <AddToCollectionDialog
        isOpen={isAddToCollectionDialogOpen}
        onClose={() => {
          setIsAddToCollectionDialogOpen(false);
        }}
        promptId={prompt.id}
        promptTitle={prompt.title}
        onSuccess={async () => {
          if (onToggleSave) {
            await onToggleSave(prompt.id, true);
          }
          setIsAddToCollectionDialogOpen(false); // Close dialog on success
        }}
      />
    </Card>
  )
}
